import { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { Upload, Download, FileText, Zap, CheckCircle, AlertCircle, Clock, Archive, Share2, Link, Copy, X, Heart } from 'lucide-react';
import { uploadFile, downloadFile, getTransferStatus, decompressFile, downloadDecompressedFile, generateShareLink, LinkRequest } from './services/api';

interface Transfer {
  id: string;
  filename: string;
  size: number;
  status: 'uploading' | 'compressing' | 'ready' | 'error';
  downloadUrl?: string;
  compressionRatio?: number;
}

interface DecompressedFile {
  decompressId: string;
  filename: string;
  originalFilename: string;
  downloadUrl: string;
}

function App() {
  const [transfers, setTransfers] = useState<Transfer[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [decompressedFiles, setDecompressedFiles] = useState<DecompressedFile[]>([]);
  const [isDecompressing, setIsDecompressing] = useState(false);
  const [shareModalOpen, setShareModalOpen] = useState(false);
  const [selectedTransfer, setSelectedTransfer] = useState<Transfer | null>(null);
  const [shareUrl, setShareUrl] = useState('');
  const [shareSettings, setShareSettings] = useState({
    expirationHours: 24,
    downloadLimit: 0,
    password: ''
  });

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    if (acceptedFiles.length === 0) return;

    const file = acceptedFiles[0];
    const transferId = Date.now().toString();

    const newTransfer: Transfer = {
      id: transferId,
      filename: file.name,
      size: file.size,
      status: 'uploading'
    };

    setTransfers(prev => [...prev, newTransfer]);
    setIsUploading(true);

    try {
      await uploadFile(file, transferId);

      setTransfers(prev => prev.map(t =>
        t.id === transferId ? { ...t, status: 'compressing' } : t
      ));

      const pollStatus = async () => {
        try {
          const status = await getTransferStatus(transferId);
          if (status.status === 'ready') {
            setTransfers(prev => prev.map(t =>
              t.id === transferId ? {
                ...t,
                status: 'ready',
                downloadUrl: status.downloadUrl,
                compressionRatio: status.compressionRatio
              } : t
            ));
          } else if (status.status === 'error') {
            setTransfers(prev => prev.map(t =>
              t.id === transferId ? { ...t, status: 'error' } : t
            ));
          } else {
            setTimeout(pollStatus, 2000);
          }
        } catch (error) {
          console.error('Error polling status:', error);
          setTransfers(prev => prev.map(t =>
            t.id === transferId ? { ...t, status: 'error' } : t
          ));
        }
      };

      setTimeout(pollStatus, 1000);
    } catch (error) {
      console.error('Upload failed:', error);
      setTransfers(prev => prev.map(t =>
        t.id === transferId ? { ...t, status: 'error' } : t
      ));
    } finally {
      setIsUploading(false);
    }
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    multiple: false,
    disabled: isUploading
  });

  const handleDownload = async (transfer: Transfer) => {
    if (!transfer.downloadUrl) return;

    try {
      await downloadFile(transfer.downloadUrl, transfer.filename);
    } catch (error) {
      console.error('Download failed:', error);
    }
  };

  const handleShare = (transfer: Transfer) => {
    setSelectedTransfer(transfer);
    setShareModalOpen(true);
    setShareUrl('');
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getStatusIcon = (status: Transfer['status']) => {
    switch (status) {
      case 'uploading':
        return <Upload className="w-4 h-4 text-blue-500 animate-pulse" />;
      case 'compressing':
        return <Zap className="w-4 h-4 text-yellow-500 animate-pulse" />;
      case 'ready':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'error':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
    }
  };

  const getStatusText = (status: Transfer['status']) => {
    switch (status) {
      case 'uploading':
        return 'Uploading';
      case 'compressing':
        return 'Compressing';
      case 'ready':
        return 'Ready';
      case 'error':
        return 'Error';
    }
  };

  // Decompression functionality
  const onDecompressDrop = useCallback(async (acceptedFiles: File[]) => {
    if (acceptedFiles.length === 0) return;

    setIsDecompressing(true);

    try {
      for (const file of acceptedFiles) {
        const decompressId = Date.now().toString() + Math.random().toString(36).substr(2, 9);

        const result = await decompressFile(file, decompressId);

        if (result.files && result.files.length > 0) {
          const newDecompressedFiles = result.files.map((f: any) => ({
            decompressId,
            filename: f.filename,
            originalFilename: file.name,
            downloadUrl: f.downloadUrl
          }));

          setDecompressedFiles(prev => [...prev, ...newDecompressedFiles]);
        }
      }
    } catch (error) {
      console.error('Decompression failed:', error);
    } finally {
      setIsDecompressing(false);
    }
  }, []);

  const { getRootProps: getDecompressRootProps, getInputProps: getDecompressInputProps, isDragActive: isDecompressDragActive } = useDropzone({
    onDrop: onDecompressDrop,
    multiple: true,
    disabled: isDecompressing,
    accept: {
      'application/octet-stream': ['.zmt']
    }
  });

  const handleDecompressedDownload = async (decompressedFile: DecompressedFile) => {
    try {
      await downloadDecompressedFile(decompressedFile.downloadUrl, decompressedFile.filename);
    } catch (error) {
      console.error('Download failed:', error);
    }
  };

  // Share modal functions
  const generateShare = async () => {
    if (!selectedTransfer) return;

    try {
      const linkRequest: LinkRequest = {
        transferId: selectedTransfer.id,
        expirationHours: shareSettings.expirationHours,
        downloadLimit: shareSettings.downloadLimit,
        password: shareSettings.password || undefined
      };

      const response = await generateShareLink(linkRequest);
      setShareUrl(response.shareUrl);
    } catch (error) {
      console.error('Failed to generate share link:', error);
    }
  };

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(shareUrl);
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
    }
  };

  const closeShareModal = () => {
    setShareModalOpen(false);
    setSelectedTransfer(null);
    setShareUrl('');
    setShareSettings({
      expirationHours: 24,
      downloadLimit: 0,
      password: ''
    });
  };

  return (
    <div className="min-h-screen bg-base-200" data-theme="fasttransfer">
      <div className="max-w-6xl mx-auto px-4 py-8">
        <h1 className="text-4xl font-bold text-center mb-8">FastTransfer</h1>

        {/* Upload Area */}
        <div className="card bg-base-100 shadow-xl mb-8">
          <div className="card-body">
            <h2 className="card-title text-2xl mb-4">
              <Upload className="w-6 h-6" />
              Upload Your File
            </h2>
            <div
              {...getRootProps()}
              className="border-2 border-dashed rounded-lg p-12 text-center cursor-pointer transition-all duration-300 border-base-300 hover:border-primary hover:bg-base-200"
            >
              <input {...getInputProps()} />
              <div className="flex flex-col items-center space-y-4">
                <div className="p-4 rounded-full bg-base-200">
                  <Upload className="w-12 h-12" />
                </div>
                {isDragActive ? (
                  <div className="text-center">
                    <p className="text-xl font-semibold text-primary">Drop it here!</p>
                    <p className="text-base-content opacity-70">Release to upload your file</p>
                  </div>
                ) : (
                  <div className="text-center space-y-2">
                    <p className="text-xl font-semibold text-base-content">
                      Drag and drop your file here
                    </p>
                    <p className="text-base-content opacity-70">or</p>
                    <button className="btn btn-primary btn-outline">
                      Browse Files
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Transfer List */}
        {transfers.length > 0 && (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="text-3xl font-bold text-base-content">Your Transfers</h2>
              <div className="badge badge-primary badge-lg">
                {transfers.length} file{transfers.length !== 1 ? 's' : ''}
              </div>
            </div>

            <div className="grid gap-4">
              {transfers.map((transfer) => (
                <div key={transfer.id} className="card bg-base-100 shadow-xl">
                  <div className="card-body">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center space-x-4">
                        <div className="avatar placeholder">
                          <div className="bg-primary text-primary-content rounded-full w-12">
                            <FileText className="w-6 h-6" />
                          </div>
                        </div>
                        <div>
                          <h3 className="card-title text-lg">{transfer.filename}</h3>
                          <div className="flex items-center space-x-2 text-sm text-base-content opacity-70">
                            <span>{formatFileSize(transfer.size)}</span>
                            {transfer.compressionRatio && (
                              <>
                                <div className="divider divider-horizontal"></div>
                                <span className="text-success font-medium">
                                  {Math.round((1 - transfer.compressionRatio) * 100)}% smaller
                                </span>
                              </>
                            )}
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center space-x-3">
                        <div className="badge badge-success gap-2">
                          {getStatusIcon(transfer.status)}
                          {getStatusText(transfer.status)}
                        </div>

                        {transfer.status === 'ready' && transfer.downloadUrl && (
                          <div className="flex space-x-2">
                            <button
                              onClick={() => handleDownload(transfer)}
                              className="btn btn-primary btn-sm"
                            >
                              <Download className="w-4 h-4" />
                              Download
                            </button>
                            <button
                              onClick={() => handleShare(transfer)}
                              className="btn btn-outline btn-sm"
                            >
                              <Share2 className="w-4 h-4" />
                              Share
                            </button>
                          </div>
                        )}
                      </div>
                    </div>

                    {transfer.status === 'compressing' && (
                      <div className="mt-4">
                        <div className="flex items-center space-x-2 text-sm text-base-content opacity-70 mb-2">
                          <Clock className="w-4 h-4" />
                          <span>Compressing with ZMT technology...</span>
                        </div>
                        <progress className="progress progress-warning w-full"></progress>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Decompression Section */}
        <div className="card bg-base-100 shadow-xl mt-8">
          <div className="card-body">
            <h2 className="card-title text-2xl mb-4">
              <Archive className="w-6 h-6" />
              Decompress ZMT Files
            </h2>
            <p className="text-base-content opacity-70 mb-6">
              Already have a ZMT compressed file? Upload it here to extract the original files.
            </p>

            <div
              {...getDecompressRootProps()}
              className="border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-all duration-300 border-base-300 hover:border-success hover:bg-base-200"
            >
              <input {...getDecompressInputProps()} />
              <div className="flex flex-col items-center space-y-4">
                <div className="p-4 rounded-full bg-base-200">
                  <Archive className="w-12 h-12" />
                </div>
                {isDecompressDragActive ? (
                  <div className="text-center">
                    <p className="text-xl font-semibold text-success">Ready to decompress!</p>
                    <p className="text-base-content opacity-70">Release to extract your files</p>
                  </div>
                ) : (
                  <div className="text-center space-y-2">
                    <p className="text-xl font-semibold text-base-content">
                      Drop your ZMT files here
                    </p>
                    <p className="text-base-content opacity-70">or</p>
                    <button className="btn btn-success btn-outline">
                      Browse ZMT Files
                    </button>
                    <div className="text-sm text-base-content opacity-60 mt-4">
                      <div className="badge badge-outline">.zmt files only</div>
                    </div>
                  </div>
                )}
                {isDecompressing && (
                  <div className="flex items-center space-x-2 text-info">
                    <span className="loading loading-spinner loading-sm"></span>
                    <span>Decompressing...</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Decompressed Files List */}
        {decompressedFiles.length > 0 && (
          <div className="card bg-base-100 shadow-xl mt-8">
            <div className="card-body">
              <h3 className="card-title text-xl mb-4">
                <CheckCircle className="w-5 h-5 text-success" />
                Decompressed Files
              </h3>
              <div className="space-y-3">
                {decompressedFiles.map((file, index) => (
                  <div key={`${file.decompressId}-${index}`} className="alert alert-success">
                    <div className="flex items-center justify-between w-full">
                      <div className="flex items-center space-x-3">
                        <FileText className="w-6 h-6" />
                        <div>
                          <p className="font-medium">{file.filename}</p>
                          <p className="text-sm opacity-70">From: {file.originalFilename}</p>
                        </div>
                      </div>
                      <button
                        onClick={() => handleDecompressedDownload(file)}
                        className="btn btn-success btn-sm"
                      >
                        <Download className="w-4 h-4" />
                        Download
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Share Modal */}
        {shareModalOpen && (
          <div className="modal modal-open">
            <div className="modal-box max-w-md">
              <div className="flex items-center justify-between mb-6">
                <h3 className="font-bold text-2xl flex items-center space-x-2">
                  <Share2 className="w-6 h-6 text-primary" />
                  <span>Share Transfer</span>
                </h3>
                <button
                  onClick={closeShareModal}
                  className="btn btn-sm btn-circle btn-ghost"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>

              {selectedTransfer && (
                <div className="alert alert-info mb-6">
                  <FileText className="w-5 h-5" />
                  <div>
                    <div className="font-bold">File to share</div>
                    <div className="text-sm">{selectedTransfer.filename}</div>
                  </div>
                </div>
              )}

              {!shareUrl ? (
                <div className="space-y-6">
                  <div className="form-control">
                    <label className="label">
                      <span className="label-text font-medium">Expiration Time</span>
                      <span className="label-text-alt">1-168 hours</span>
                    </label>
                    <input
                      type="number"
                      value={shareSettings.expirationHours}
                      onChange={(e) => setShareSettings(prev => ({ ...prev, expirationHours: parseInt(e.target.value) || 24 }))}
                      className="input input-bordered w-full"
                      min="1"
                      max="168"
                      placeholder="24"
                    />
                  </div>

                  <div className="form-control">
                    <label className="label">
                      <span className="label-text font-medium">Download Limit</span>
                      <span className="label-text-alt">0 = unlimited</span>
                    </label>
                    <input
                      type="number"
                      value={shareSettings.downloadLimit}
                      onChange={(e) => setShareSettings(prev => ({ ...prev, downloadLimit: parseInt(e.target.value) || 0 }))}
                      className="input input-bordered w-full"
                      min="0"
                      placeholder="0"
                    />
                  </div>

                  <div className="form-control">
                    <label className="label">
                      <span className="label-text font-medium">Password Protection</span>
                      <span className="label-text-alt">Optional</span>
                    </label>
                    <input
                      type="password"
                      value={shareSettings.password}
                      onChange={(e) => setShareSettings(prev => ({ ...prev, password: e.target.value }))}
                      className="input input-bordered w-full"
                      placeholder="Leave empty for no password"
                    />
                  </div>

                  <button
                    onClick={generateShare}
                    className="btn btn-primary w-full"
                  >
                    <Link className="w-5 h-5" />
                    Generate Share Link
                  </button>
                </div>
              ) : (
                <div className="space-y-6">
                  <div className="alert alert-success">
                    <CheckCircle className="w-6 h-6" />
                    <div>
                      <div className="font-bold">Share link generated!</div>
                      <div className="text-sm">Your file is ready to share</div>
                    </div>
                  </div>

                  <div className="form-control">
                    <label className="label">
                      <span className="label-text font-medium">Share Link</span>
                    </label>
                    <div className="join w-full">
                      <input
                        type="text"
                        value={shareUrl}
                        readOnly
                        className="input input-bordered join-item flex-1 text-sm"
                      />
                      <button
                        onClick={copyToClipboard}
                        className="btn btn-primary join-item"
                      >
                        <Copy className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </div>
            <div className="modal-backdrop" onClick={closeShareModal}></div>
          </div>
        )}
      </div>
    </div>
  );
}

export default App;
