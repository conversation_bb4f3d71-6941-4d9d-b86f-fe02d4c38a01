import { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { Upload, Download, FileText, Zap, CheckCircle, AlertCircle, Clock, Archive, Share2, Link, Copy, X, Heart, HardDrive, Settings, Info } from 'lucide-react';
import { uploadFile, downloadFile, getTransferStatus, decompressFile, downloadDecompressedFile, generateShareLink, LinkRequest } from './services/api';

interface Transfer {
  id: string;
  filename: string;
  size: number;
  status: 'uploading' | 'compressing' | 'ready' | 'error';
  downloadUrl?: string;
  compressionRatio?: number;
}

interface DecompressedFile {
  decompressId: string;
  filename: string;
  originalFilename: string;
  downloadUrl: string;
}

function App() {
  const [transfers, setTransfers] = useState<Transfer[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [decompressedFiles, setDecompressedFiles] = useState<DecompressedFile[]>([]);
  const [isDecompressing, setIsDecompressing] = useState(false);
  const [shareModalOpen, setShareModalOpen] = useState(false);
  const [selectedTransfer, setSelectedTransfer] = useState<Transfer | null>(null);
  const [shareUrl, setShareUrl] = useState('');
  const [shareSettings, setShareSettings] = useState({
    expirationHours: 24,
    downloadLimit: 0,
    password: ''
  });

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    if (acceptedFiles.length === 0) return;

    const file = acceptedFiles[0];
    const transferId = Date.now().toString();

    const newTransfer: Transfer = {
      id: transferId,
      filename: file.name,
      size: file.size,
      status: 'uploading'
    };

    setTransfers(prev => [...prev, newTransfer]);
    setIsUploading(true);

    try {
      await uploadFile(file, transferId);

      setTransfers(prev => prev.map(t =>
        t.id === transferId ? { ...t, status: 'compressing' } : t
      ));

      const pollStatus = async () => {
        try {
          const status = await getTransferStatus(transferId);
          if (status.status === 'ready') {
            setTransfers(prev => prev.map(t =>
              t.id === transferId ? {
                ...t,
                status: 'ready',
                downloadUrl: status.downloadUrl,
                compressionRatio: status.compressionRatio
              } : t
            ));
          } else if (status.status === 'error') {
            setTransfers(prev => prev.map(t =>
              t.id === transferId ? { ...t, status: 'error' } : t
            ));
          } else {
            setTimeout(pollStatus, 2000);
          }
        } catch (error) {
          console.error('Error polling status:', error);
          setTransfers(prev => prev.map(t =>
            t.id === transferId ? { ...t, status: 'error' } : t
          ));
        }
      };

      setTimeout(pollStatus, 1000);
    } catch (error) {
      console.error('Upload failed:', error);
      setTransfers(prev => prev.map(t =>
        t.id === transferId ? { ...t, status: 'error' } : t
      ));
    } finally {
      setIsUploading(false);
    }
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    multiple: false,
    disabled: isUploading
  });

  const handleDownload = async (transfer: Transfer) => {
    if (!transfer.downloadUrl) return;

    try {
      await downloadFile(transfer.downloadUrl, transfer.filename);
    } catch (error) {
      console.error('Download failed:', error);
    }
  };

  const handleShare = (transfer: Transfer) => {
    setSelectedTransfer(transfer);
    setShareModalOpen(true);
    setShareUrl('');
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getStatusIcon = (status: Transfer['status']) => {
    switch (status) {
      case 'uploading':
        return <Upload className="w-4 h-4 text-blue-500 animate-pulse" />;
      case 'compressing':
        return <Zap className="w-4 h-4 text-yellow-500 animate-pulse" />;
      case 'ready':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'error':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
    }
  };

  const getStatusText = (status: Transfer['status']) => {
    switch (status) {
      case 'uploading':
        return 'Uploading';
      case 'compressing':
        return 'Compressing';
      case 'ready':
        return 'Ready';
      case 'error':
        return 'Error';
    }
  };

  // Decompression functionality
  const onDecompressDrop = useCallback(async (acceptedFiles: File[]) => {
    if (acceptedFiles.length === 0) return;

    setIsDecompressing(true);

    try {
      for (const file of acceptedFiles) {
        const decompressId = Date.now().toString() + Math.random().toString(36).substr(2, 9);

        const result = await decompressFile(file, decompressId);

        if (result.files && result.files.length > 0) {
          const newDecompressedFiles = result.files.map((f: any) => ({
            decompressId,
            filename: f.filename,
            originalFilename: file.name,
            downloadUrl: f.downloadUrl
          }));

          setDecompressedFiles(prev => [...prev, ...newDecompressedFiles]);
        }
      }
    } catch (error) {
      console.error('Decompression failed:', error);
    } finally {
      setIsDecompressing(false);
    }
  }, []);

  const { getRootProps: getDecompressRootProps, getInputProps: getDecompressInputProps, isDragActive: isDecompressDragActive } = useDropzone({
    onDrop: onDecompressDrop,
    multiple: true,
    disabled: isDecompressing,
    accept: {
      'application/octet-stream': ['.zmt']
    }
  });

  const handleDecompressedDownload = async (decompressedFile: DecompressedFile) => {
    try {
      await downloadDecompressedFile(decompressedFile.downloadUrl, decompressedFile.filename);
    } catch (error) {
      console.error('Download failed:', error);
    }
  };

  // Share modal functions
  const generateShare = async () => {
    if (!selectedTransfer) return;

    try {
      const linkRequest: LinkRequest = {
        transferId: selectedTransfer.id,
        expirationHours: shareSettings.expirationHours,
        downloadLimit: shareSettings.downloadLimit,
        password: shareSettings.password || undefined
      };

      const response = await generateShareLink(linkRequest);
      setShareUrl(response.shareUrl);
    } catch (error) {
      console.error('Failed to generate share link:', error);
    }
  };

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(shareUrl);
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
    }
  };

  const closeShareModal = () => {
    setShareModalOpen(false);
    setSelectedTransfer(null);
    setShareUrl('');
    setShareSettings({
      expirationHours: 24,
      downloadLimit: 0,
      password: ''
    });
  };

  return (
    <div className="min-h-screen bg-base-200" data-theme="fasttransfer">
      {/* Header */}
      <header className="bg-base-100 shadow-sm border-b border-base-300">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            {/* Logo and Brand */}
            <div className="flex items-center space-x-3">
              <div className="flex items-center space-x-2">
                <div className="p-2 bg-gradient-to-br from-floppy-pink to-floppy-purple rounded-xl">
                  <HardDrive className="w-6 h-6 text-white" />
                </div>
                <div className="p-2 bg-gradient-to-br from-floppy-purple to-floppy-blue rounded-xl">
                  <HardDrive className="w-6 h-6 text-white" />
                </div>
              </div>
              <div>
                <h1 className="text-2xl font-bold text-floppy-green">FLOPPY DISK</h1>
                <p className="text-sm text-base-content/70">Future tech. Floppy size.</p>
              </div>
            </div>

            {/* Navigation */}
            <nav className="hidden md:flex items-center space-x-6">
              <a href="#features" className="text-base-content hover:text-primary transition-colors">Features</a>
              <a href="#how-it-works" className="text-base-content hover:text-primary transition-colors">How it works</a>
              <div className="dropdown dropdown-end">
                <div tabIndex={0} role="button" className="btn btn-ghost btn-sm">
                  Theme: Standard
                  <Settings className="w-4 h-4 ml-1" />
                </div>
                <ul tabIndex={0} className="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-52">
                  <li><a>Standard</a></li>
                  <li><a>Dark</a></li>
                  <li><a>Light</a></li>
                </ul>
              </div>
            </nav>
          </div>
        </div>
      </header>

      <div className="max-w-6xl mx-auto px-4 py-8">
        {/* Hero Section */}
        <div className="grid lg:grid-cols-2 gap-12 items-center mb-16">
          {/* Left Side - Hero Text */}
          <div className="space-y-8">
            <div className="space-y-4">
              <h1 className="text-6xl lg:text-7xl font-black text-base-content leading-tight">
                SEND<br />
                <span className="text-floppy-pink">HUGE</span><br />
                FILES
              </h1>

              {/* Feature Badges */}
              <div className="flex flex-wrap gap-3">
                <div className="badge badge-lg bg-floppy-blue text-white border-0 px-4 py-3">
                  NO SIGNUP NEEDED
                </div>
                <div className="badge badge-lg bg-floppy-yellow text-black border-0 px-4 py-3">
                  INSANE COMPRESSION
                </div>
                <div className="badge badge-lg bg-floppy-green text-white border-0 px-4 py-3">
                  LIGHTNING FAST
                </div>
              </div>
            </div>

            {/* Feature Description */}
            <div className="space-y-4">
              <p className="text-xl text-base-content/80">
                Forget tiny limits. <span className="font-bold text-floppy-blue">1 TERABYTE</span> Free. Yeah, our compression algorithm is <span className="font-bold text-floppy-purple">THAT GOOD</span>.
              </p>

              {/* Fun Callout */}
              <div className="p-4 border-2 border-dashed border-floppy-pink rounded-2xl bg-floppy-pink/5">
                <div className="flex items-center space-x-2 text-floppy-pink">
                  <Heart className="w-5 h-5 fill-current" />
                  <span className="font-medium">This is a flex, that's why it's free</span>
                </div>
                <p className="text-sm text-base-content/70 mt-1">We're the best, you benefit</p>
              </div>

              {/* CTA Button */}
              <button className="btn btn-lg bg-black text-white hover:bg-gray-800 border-0 rounded-2xl px-8">
                <Upload className="w-5 h-5" />
                START TRANSFERRING NOW
              </button>

              <p className="text-sm text-base-content/60">
                No account required • No credit card • No bullsh*t
              </p>
            </div>
          </div>

          {/* Right Side - Upload Preview */}
          <div className="relative">
            <div className="bg-gradient-to-br from-blue-50 to-purple-50 rounded-3xl p-8 border-2 border-dashed border-blue-300">
              <div className="text-center space-y-4">
                <div className="w-16 h-16 mx-auto bg-blue-100 rounded-full flex items-center justify-center">
                  <Upload className="w-8 h-8 text-blue-600" />
                </div>
                <div>
                  <p className="text-lg font-semibold text-gray-700">Drop files here</p>
                  <p className="text-sm text-gray-500">or click to browse</p>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-floppy-blue">UP TO 1TB</div>
                  <p className="text-sm text-gray-600">Seriously. We're not kidding.</p>
                </div>

                {/* Upload Stats */}
                <div className="bg-white rounded-xl p-4 shadow-sm">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">5sec</span>
                    <span className="text-floppy-green font-bold">99.9%</span>
                  </div>
                  <div className="flex items-center justify-between text-xs text-gray-500 mt-1">
                    <span>Average Upload</span>
                    <span>Compression Rate</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Upload Area */}
        <div className="mb-12">
          <div
            {...getRootProps()}
            className={`
              border-2 border-dashed rounded-3xl p-12 text-center cursor-pointer transition-all duration-300
              ${isDragActive
                ? 'border-floppy-blue bg-blue-50 scale-105'
                : 'border-blue-300 hover:border-floppy-blue hover:bg-blue-50/50'
              }
              ${isUploading ? 'pointer-events-none opacity-75' : ''}
            `}
          >
            <input {...getInputProps()} />
            <div className="flex flex-col items-center space-y-6">
              <div className={`p-6 rounded-full transition-all duration-300 ${
                isDragActive ? 'bg-floppy-blue scale-110' : 'bg-blue-100'
              }`}>
                <Upload className={`w-16 h-16 ${isDragActive ? 'text-white' : 'text-floppy-blue'}`} />
              </div>

              {isDragActive ? (
                <div className="text-center space-y-2">
                  <p className="text-2xl font-bold text-floppy-blue">Drop it here!</p>
                  <p className="text-base-content/70">Release to upload your file</p>
                </div>
              ) : (
                <div className="text-center space-y-4">
                  <div>
                    <p className="text-2xl font-bold text-base-content mb-2">
                      Drop files here
                    </p>
                    <p className="text-base-content/70 mb-4">or click to browse</p>
                  </div>

                  <div className="space-y-3">
                    <div className="text-4xl font-black text-floppy-blue">UP TO 1TB</div>
                    <p className="text-base-content/70">Seriously. We're not kidding.</p>
                  </div>

                  {/* Upload Stats */}
                  <div className="flex justify-center space-x-8 mt-6">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-floppy-green">5sec</div>
                      <div className="text-sm text-base-content/60">Average Upload</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-floppy-green">99.9%</div>
                      <div className="text-sm text-base-content/60">Compression Rate</div>
                    </div>
                  </div>
                </div>
              )}

              {isUploading && (
                <div className="flex items-center space-x-3 text-floppy-blue">
                  <span className="loading loading-spinner loading-md"></span>
                  <span className="font-medium">Uploading your file...</span>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Transfer List */}
        {transfers.length > 0 && (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="text-3xl font-bold text-base-content">Your Transfers</h2>
              <div className="badge badge-primary badge-lg">
                {transfers.length} file{transfers.length !== 1 ? 's' : ''}
              </div>
            </div>

            <div className="grid gap-6">
              {transfers.map((transfer) => (
                <div key={transfer.id} className="bg-base-100 rounded-3xl shadow-xl border border-base-300 overflow-hidden">
                  <div className="p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center space-x-4">
                        <div className="avatar placeholder">
                          <div className="bg-primary text-primary-content rounded-full w-12">
                            <FileText className="w-6 h-6" />
                          </div>
                        </div>
                        <div>
                          <h3 className="card-title text-lg">{transfer.filename}</h3>
                          <div className="flex items-center space-x-3 text-sm">
                            <span className="text-base-content/70">{formatFileSize(transfer.size)}</span>
                            {transfer.compressionRatio && (
                              <div className="flex items-center space-x-2">
                                <div className="divider divider-horizontal"></div>
                                <div className="badge badge-success gap-1 font-bold">
                                  <Zap className="w-3 h-3" />
                                  {Math.round((1 - transfer.compressionRatio) * 100)}% SMALLER
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center space-x-3">
                        <div className={`badge badge-lg gap-2 font-bold ${
                          transfer.status === 'ready' ? 'bg-floppy-green text-white' :
                          transfer.status === 'compressing' ? 'bg-floppy-yellow text-black' :
                          transfer.status === 'uploading' ? 'bg-floppy-blue text-white' :
                          'bg-red-500 text-white'
                        }`}>
                          {getStatusIcon(transfer.status)}
                          {getStatusText(transfer.status)}
                        </div>

                        {transfer.status === 'ready' && transfer.downloadUrl && (
                          <div className="flex space-x-3">
                            <button
                              onClick={() => handleDownload(transfer)}
                              className="btn bg-floppy-blue hover:bg-floppy-blue/80 text-white border-0 rounded-2xl btn-sm"
                            >
                              <Download className="w-4 h-4" />
                              Download
                            </button>
                            <button
                              onClick={() => handleShare(transfer)}
                              className="btn bg-floppy-purple hover:bg-floppy-purple/80 text-white border-0 rounded-2xl btn-sm"
                            >
                              <Share2 className="w-4 h-4" />
                              Share
                            </button>
                          </div>
                        )}
                      </div>
                    </div>

                    {transfer.status === 'compressing' && (
                      <div className="mt-4">
                        <div className="flex items-center space-x-2 text-sm text-base-content opacity-70 mb-2">
                          <Clock className="w-4 h-4" />
                          <span>Compressing with ZMT technology...</span>
                        </div>
                        <progress className="progress progress-warning w-full"></progress>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Compression Showcase */}
        {transfers.some(t => t.compressionRatio) && (
          <div className="bg-gradient-to-r from-floppy-green/10 to-floppy-blue/10 rounded-3xl p-8 mb-8 border border-floppy-green/20">
            <div className="text-center space-y-4">
              <h3 className="text-2xl font-bold text-base-content">🚀 Compression Magic in Action</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="text-4xl font-black text-floppy-green">
                    {transfers.filter(t => t.compressionRatio).length > 0
                      ? Math.round((1 - transfers.filter(t => t.compressionRatio)[0].compressionRatio!) * 100)
                      : 99}%
                  </div>
                  <div className="text-sm text-base-content/70">Compression Rate</div>
                </div>
                <div className="text-center">
                  <div className="text-4xl font-black text-floppy-blue">
                    {transfers.filter(t => t.status === 'ready').length}
                  </div>
                  <div className="text-sm text-base-content/70">Files Compressed</div>
                </div>
                <div className="text-center">
                  <div className="text-4xl font-black text-floppy-purple">
                    {Math.round(transfers.reduce((acc, t) => acc + (t.compressionRatio ? (t.size * (1 - t.compressionRatio)) : 0), 0) / 1024 / 1024)}MB
                  </div>
                  <div className="text-sm text-base-content/70">Space Saved</div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Decompression Section */}
        <div className="bg-base-100 rounded-3xl shadow-xl border border-base-300 mt-8">
          <div className="p-8">
            <h2 className="text-3xl font-bold mb-2 flex items-center space-x-3">
              <div className="p-2 bg-floppy-orange rounded-xl">
                <Archive className="w-6 h-6 text-white" />
              </div>
              <span>Decompress ZMT Files</span>
            </h2>
            <p className="text-base-content/70 mb-8">
              Already have a ZMT compressed file? Upload it here to extract the original files.
            </p>

            <div
              {...getDecompressRootProps()}
              className={`
                border-2 border-dashed rounded-3xl p-8 text-center cursor-pointer transition-all duration-300
                ${isDecompressDragActive
                  ? 'border-floppy-orange bg-orange-50 scale-105'
                  : 'border-orange-300 hover:border-floppy-orange hover:bg-orange-50/50'
                }
                ${isDecompressing ? 'pointer-events-none opacity-75' : ''}
              `}
            >
              <input {...getDecompressInputProps()} />
              <div className="flex flex-col items-center space-y-6">
                <div className={`p-6 rounded-full transition-all duration-300 ${
                  isDecompressDragActive ? 'bg-floppy-orange scale-110' : 'bg-orange-100'
                }`}>
                  <Archive className={`w-16 h-16 ${isDecompressDragActive ? 'text-white' : 'text-floppy-orange'}`} />
                </div>
                {isDecompressDragActive ? (
                  <div className="text-center">
                    <p className="text-2xl font-bold text-floppy-orange">Ready to decompress!</p>
                    <p className="text-base-content/70">Release to extract your files</p>
                  </div>
                ) : (
                  <div className="text-center space-y-4">
                    <div>
                      <p className="text-2xl font-bold text-base-content mb-2">
                        Drop your ZMT files here
                      </p>
                      <p className="text-base-content/70">or</p>
                    </div>
                    <button className="btn bg-floppy-orange hover:bg-floppy-orange/80 text-white border-0 rounded-2xl">
                      Browse ZMT Files
                    </button>
                    <div className="badge bg-orange-100 text-floppy-orange border-floppy-orange/30 font-medium">
                      .zmt files only
                    </div>
                  </div>
                )}
                {isDecompressing && (
                  <div className="flex items-center space-x-3 text-floppy-orange">
                    <span className="loading loading-spinner loading-md"></span>
                    <span className="font-medium">Decompressing your files...</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Decompressed Files List */}
        {decompressedFiles.length > 0 && (
          <div className="bg-base-100 rounded-3xl shadow-xl border border-base-300 mt-8">
            <div className="p-8">
              <h3 className="text-2xl font-bold mb-6 flex items-center space-x-3">
                <div className="p-2 bg-floppy-green rounded-xl">
                  <CheckCircle className="w-5 h-5 text-white" />
                </div>
                <span>Decompressed Files</span>
              </h3>
              <div className="space-y-4">
                {decompressedFiles.map((file, index) => (
                  <div key={`${file.decompressId}-${index}`} className="bg-green-50 border border-green-200 rounded-2xl p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <div className="p-2 bg-floppy-green rounded-xl">
                          <FileText className="w-5 h-5 text-white" />
                        </div>
                        <div>
                          <p className="font-bold text-base-content">{file.filename}</p>
                          <p className="text-sm text-base-content/70">From: {file.originalFilename}</p>
                        </div>
                      </div>
                      <button
                        onClick={() => handleDecompressedDownload(file)}
                        className="btn bg-floppy-green hover:bg-floppy-green/80 text-white border-0 rounded-2xl btn-sm"
                      >
                        <Download className="w-4 h-4" />
                        Download
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Share Modal */}
        {shareModalOpen && (
          <div className="modal modal-open">
            <div className="modal-box max-w-md rounded-3xl">
              <div className="flex items-center justify-between mb-6">
                <h3 className="font-bold text-2xl flex items-center space-x-3">
                  <div className="p-2 bg-floppy-purple rounded-xl">
                    <Share2 className="w-6 h-6 text-white" />
                  </div>
                  <span>Share Transfer</span>
                </h3>
                <button
                  onClick={closeShareModal}
                  className="btn btn-sm btn-circle bg-base-200 hover:bg-base-300 border-0"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>

              {selectedTransfer && (
                <div className="alert alert-info mb-6">
                  <FileText className="w-5 h-5" />
                  <div>
                    <div className="font-bold">File to share</div>
                    <div className="text-sm">{selectedTransfer.filename}</div>
                  </div>
                </div>
              )}

              {!shareUrl ? (
                <div className="space-y-6">
                  <div className="form-control">
                    <label className="label">
                      <span className="label-text font-medium">Expiration Time</span>
                      <span className="label-text-alt">1-168 hours</span>
                    </label>
                    <input
                      type="number"
                      value={shareSettings.expirationHours}
                      onChange={(e) => setShareSettings(prev => ({ ...prev, expirationHours: parseInt(e.target.value) || 24 }))}
                      className="input input-bordered w-full"
                      min="1"
                      max="168"
                      placeholder="24"
                    />
                  </div>

                  <div className="form-control">
                    <label className="label">
                      <span className="label-text font-medium">Download Limit</span>
                      <span className="label-text-alt">0 = unlimited</span>
                    </label>
                    <input
                      type="number"
                      value={shareSettings.downloadLimit}
                      onChange={(e) => setShareSettings(prev => ({ ...prev, downloadLimit: parseInt(e.target.value) || 0 }))}
                      className="input input-bordered w-full"
                      min="0"
                      placeholder="0"
                    />
                  </div>

                  <div className="form-control">
                    <label className="label">
                      <span className="label-text font-medium">Password Protection</span>
                      <span className="label-text-alt">Optional</span>
                    </label>
                    <input
                      type="password"
                      value={shareSettings.password}
                      onChange={(e) => setShareSettings(prev => ({ ...prev, password: e.target.value }))}
                      className="input input-bordered w-full"
                      placeholder="Leave empty for no password"
                    />
                  </div>

                  <button
                    onClick={generateShare}
                    className="btn bg-floppy-purple hover:bg-floppy-purple/80 text-white border-0 rounded-2xl w-full"
                  >
                    <Link className="w-5 h-5" />
                    Generate Share Link
                  </button>
                </div>
              ) : (
                <div className="space-y-6">
                  <div className="alert alert-success">
                    <CheckCircle className="w-6 h-6" />
                    <div>
                      <div className="font-bold">Share link generated!</div>
                      <div className="text-sm">Your file is ready to share</div>
                    </div>
                  </div>

                  <div className="form-control">
                    <label className="label">
                      <span className="label-text font-medium">Share Link</span>
                    </label>
                    <div className="join w-full">
                      <input
                        type="text"
                        value={shareUrl}
                        readOnly
                        className="input input-bordered join-item flex-1 text-sm"
                      />
                      <button
                        onClick={copyToClipboard}
                        className="btn bg-floppy-blue hover:bg-floppy-blue/80 text-white border-0 join-item"
                      >
                        <Copy className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </div>
            <div className="modal-backdrop" onClick={closeShareModal}></div>
          </div>
        )}
      </div>
    </div>
  );
}

export default App;
