import express from 'express';
import cors from 'cors';
import multer from 'multer';
import { v4 as uuidv4 } from 'uuid';
import path from 'path';
import fs from 'fs';
import { exec } from 'child_process';
import { promisify } from 'util';

const app = express();
const port = process.env.PORT || 3000;

// Promisify exec for async/await usage
const execAsync = promisify(exec);

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(__dirname, '../uploads');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const transferId = req.body.transferId || uuidv4();
    cb(null, `${transferId}-${file.originalname}`);
  }
});

// Function to compress file using ZMT
async function compressFileWithZMT(inputFilePath: string, transferId: string): Promise<{ compressedPath: string; compressionRatio: number }> {
  const inputDir = path.dirname(inputFilePath);
  const inputFileName = path.basename(inputFilePath);
  const compressedFileName = `${transferId}.zmt`;
  const compressedPath = path.join(inputDir, compressedFileName);

  // Get the absolute path to the ZMT binary
  const zmtBinaryPath = path.join(__dirname, '../../../scripts/zmt');

  try {
    // Get original file size
    const originalStats = fs.statSync(inputFilePath);
    const originalSize = originalStats.size;

    // Run ZMT compression with relative path by changing to the input directory
    // This ensures the file is stored with a relative path for proper extraction
    const command = `cd "${inputDir}" && "${zmtBinaryPath}" a "${compressedFileName}" "${inputFileName}"`;
    console.log(`Running ZMT compression: ${command}`);

    const { stdout, stderr } = await execAsync(command);
    console.log('ZMT compression output:', stdout);
    if (stderr) {
      console.log('ZMT compression stderr:', stderr);
    }

    // Check if compressed file was created
    if (!fs.existsSync(compressedPath)) {
      throw new Error('Compressed file was not created');
    }

    // Get compressed file size
    const compressedStats = fs.statSync(compressedPath);
    const compressedSize = compressedStats.size;

    // Calculate compression ratio (percentage reduction)
    const compressionRatio = (originalSize - compressedSize) / originalSize;

    console.log(`Compression completed: ${originalSize} bytes -> ${compressedSize} bytes (${(compressionRatio * 100).toFixed(1)}% reduction)`);

    return {
      compressedPath,
      compressionRatio
    };
  } catch (error) {
    console.error('ZMT compression failed:', error);
    throw error;
  }
}

// Function to decompress ZMT file
async function decompressZMTFile(compressedFilePath: string, outputDir: string): Promise<string[]> {
  // Get the absolute path to the ZMT binary
  const zmtBinaryPath = path.join(__dirname, '../../../scripts/zmt');

  try {
    // Run ZMT extraction with force overwrite: zmt x archive.zmt -force
    const command = `cd "${outputDir}" && "${zmtBinaryPath}" x "${compressedFilePath}" -force`;
    console.log(`Running ZMT decompression: ${command}`);

    const { stdout, stderr } = await execAsync(command);
    console.log('ZMT decompression output:', stdout);
    if (stderr) {
      console.log('ZMT decompression stderr:', stderr);
    }

    // List files in the output directory to see what was extracted
    const extractedFiles: string[] = [];
    const files = fs.readdirSync(outputDir);

    for (const file of files) {
      const filePath = path.join(outputDir, file);
      const stats = fs.statSync(filePath);
      if (stats.isFile()) {
        extractedFiles.push(file);
      }
    }

    console.log(`Decompression completed. Extracted files: ${extractedFiles.join(', ')}`);

    return extractedFiles;
  } catch (error) {
    console.error('ZMT decompression failed:', error);
    throw error;
  }
}

const upload = multer({ storage });

// Middleware
app.use(cors());
app.use(express.json());

// In-memory storage for demo purposes (in production, this would be DynamoDB)
interface Transfer {
  transferId: string;
  filename: string;
  originalName: string;
  size: number;
  status: 'uploading' | 'compressing' | 'ready' | 'error';
  downloadUrl?: string;
  compressionRatio?: number;
  createdAt: Date;
  filePath?: string;
  compressedPath?: string;
  expiresAt?: number;
  downloadLimit?: number;
  downloadCount?: number;
  password?: string;
}

const transfers: Map<string, Transfer> = new Map();

// Routes

// Upload endpoint
app.post('/api/upload', upload.single('file'), (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No file uploaded' });
    }

    const transferId = req.body.transferId || uuidv4();
    const transfer: Transfer = {
      transferId,
      filename: req.file.filename,
      originalName: req.file.originalname,
      size: req.file.size,
      status: 'uploading',
      createdAt: new Date(),
      filePath: req.file.path
    };

    transfers.set(transferId, transfer);

    // Start real ZMT compression
    setTimeout(async () => {
      const currentTransfer = transfers.get(transferId);
      if (currentTransfer) {
        currentTransfer.status = 'compressing';
        transfers.set(transferId, currentTransfer);

        try {
          // Perform real ZMT compression
          const { compressedPath, compressionRatio } = await compressFileWithZMT(currentTransfer.filePath!, transferId);

          const finalTransfer = transfers.get(transferId);
          if (finalTransfer) {
            finalTransfer.status = 'ready';
            finalTransfer.downloadUrl = `/api/download/${transferId}`;
            finalTransfer.compressionRatio = compressionRatio;
            finalTransfer.compressedPath = compressedPath;
            transfers.set(transferId, finalTransfer);
          }
        } catch (error) {
          console.error('Compression failed:', error);
          const errorTransfer = transfers.get(transferId);
          if (errorTransfer) {
            errorTransfer.status = 'error';
            transfers.set(transferId, errorTransfer);
          }
        }
      }
    }, 1000); // 1 second upload processing

    res.json({
      transferId,
      message: 'File uploaded successfully'
    });
  } catch (error) {
    console.error('Upload error:', error);
    res.status(500).json({ error: 'Upload failed' });
  }
});

// Get transfer status
app.get('/api/transfer/:transferId/status', (req, res) => {
  const { transferId } = req.params;
  const transfer = transfers.get(transferId);

  if (!transfer) {
    return res.status(404).json({ error: 'Transfer not found' });
  }

  res.json({
    transferId: transfer.transferId,
    status: transfer.status,
    downloadUrl: transfer.downloadUrl,
    compressionRatio: transfer.compressionRatio,
    filename: transfer.originalName,
    size: transfer.size
  });
});

// Download endpoint
app.get('/api/download/:transferId', (req, res) => {
  const { transferId } = req.params;
  const transfer = transfers.get(transferId);

  if (!transfer) {
    return res.status(404).json({ error: 'Transfer not found' });
  }

  if (transfer.status !== 'ready') {
    return res.status(400).json({ error: 'Transfer not ready for download' });
  }

  // Use compressed file if available, otherwise fall back to original
  const fileToDownload = transfer.compressedPath || transfer.filePath;

  if (!fileToDownload || !fs.existsSync(fileToDownload)) {
    return res.status(404).json({ error: 'File not found' });
  }

  // Set headers for file download
  res.setHeader('Content-Disposition', `attachment; filename="${transfer.originalName}.zmt"`);
  res.setHeader('Content-Type', 'application/octet-stream');

  // Stream the compressed file
  const fileStream = fs.createReadStream(fileToDownload);
  fileStream.pipe(res);
});

// Decompress endpoint
app.post('/api/decompress', upload.single('file'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No ZMT file uploaded' });
    }

    const decompressId = uuidv4();
    const outputDir = path.join(__dirname, '../decompressed', decompressId);

    // Create output directory
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    // Decompress the ZMT file
    const extractedFiles = await decompressZMTFile(req.file.path, outputDir);

    // Clean up uploaded ZMT file
    fs.unlinkSync(req.file.path);

    res.json({
      decompressId,
      extractedFiles,
      message: 'File decompressed successfully'
    });
  } catch (error) {
    console.error('Decompression error:', error);
    res.status(500).json({ error: 'Decompression failed' });
  }
});

// Download decompressed file
app.get('/api/decompress/:decompressId/:filename', (req, res) => {
  const { decompressId, filename } = req.params;
  const filePath = path.join(__dirname, '../decompressed', decompressId, filename);

  if (!fs.existsSync(filePath)) {
    return res.status(404).json({ error: 'Decompressed file not found' });
  }

  // Set headers for file download
  res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
  res.setHeader('Content-Type', 'application/octet-stream');

  // Stream the decompressed file
  const fileStream = fs.createReadStream(filePath);
  fileStream.pipe(res);
});

// Get all transfers
app.get('/api/transfers', (req, res) => {
  const transferList = Array.from(transfers.values()).map(transfer => ({
    transferId: transfer.transferId,
    status: transfer.status,
    downloadUrl: transfer.downloadUrl,
    compressionRatio: transfer.compressionRatio,
    filename: transfer.originalName,
    size: transfer.size,
    createdAt: transfer.createdAt
  }));

  res.json(transferList);
});

// Generate share link
app.post('/api/link', (req, res) => {
  const { transferId, expirationHours, downloadLimit, password } = req.body;

  if (!transferId) {
    return res.status(400).json({ error: 'Transfer ID is required' });
  }

  const transfer = transfers.get(transferId);
  if (!transfer) {
    return res.status(404).json({ error: 'Transfer not found' });
  }

  if (transfer.status !== 'ready') {
    return res.status(400).json({ error: `Transfer is not ready for sharing. Current status: ${transfer.status}` });
  }

  // Update transfer with link settings
  if (expirationHours) {
    transfer.expiresAt = Date.now() + (expirationHours * 60 * 60 * 1000);
  }
  if (downloadLimit !== undefined) {
    transfer.downloadLimit = downloadLimit;
  }
  if (password) {
    transfer.password = password; // In production, this should be hashed
  }

  // Generate share URL
  const baseUrl = `${req.protocol}://${req.get('host')}`;
  const shareUrl = `${baseUrl}/share/${transferId}`;

  res.json({
    transferId,
    shareUrl,
    expiresAt: transfer.expiresAt,
    downloadLimit: transfer.downloadLimit,
  });
});

// Get transfer info for shared link
app.get('/api/transfer/:transferId/info', (req, res) => {
  const { transferId } = req.params;
  const transfer = transfers.get(transferId);

  if (!transfer) {
    return res.status(404).json({ error: 'Transfer not found' });
  }

  // Check if transfer is expired
  if (transfer.expiresAt && Date.now() > transfer.expiresAt) {
    return res.status(410).json({ error: 'Transfer has expired' });
  }

  // Return public transfer info (without sensitive data)
  const publicInfo = {
    transferId: transfer.transferId,
    status: transfer.status,
    originalName: transfer.originalName,
    size: transfer.size,
    compressionRatio: transfer.compressionRatio,
    createdAt: transfer.createdAt,
    expiresAt: transfer.expiresAt,
    downloadCount: transfer.downloadCount || 0,
    downloadLimit: transfer.downloadLimit,
    hasPassword: !!transfer.password,
  };

  res.json(publicInfo);
});

// Delete transfer
app.delete('/api/transfer/:transferId', (req, res) => {
  const { transferId } = req.params;
  const transfer = transfers.get(transferId);

  if (!transfer) {
    return res.status(404).json({ error: 'Transfer not found' });
  }

  // Delete original file if it exists
  if (transfer.filePath && fs.existsSync(transfer.filePath)) {
    fs.unlinkSync(transfer.filePath);
  }

  // Delete compressed file if it exists
  if (transfer.compressedPath && fs.existsSync(transfer.compressedPath)) {
    fs.unlinkSync(transfer.compressedPath);
  }

  transfers.delete(transferId);
  res.json({ message: 'Transfer deleted successfully' });
});

// Health check
app.get('/api/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// Start server
app.listen(port, () => {
  console.log(`FastTransfer backend server running on port ${port}`);
  console.log(`API endpoints available at http://localhost:${port}/api`);
});

export default app;
