import { SQSE<PERSON>, SQSRecord } from 'aws-lambda';
import { GetCommand, UpdateCommand, PutCommand } from '@aws-sdk/lib-dynamodb';
import { SendMessageCommand } from '@aws-sdk/client-sqs';

import { docClient, sqsClient, ENV } from '../lib/aws-clients';
import { generateJobId } from '../lib/utils';
import { 
  Transfer,
  TransferStatus,
  Job,
  JobType,
  JobStatus
} from '../types';

// Handler for processing compression job requests
export const compressionJobHandler = async (event: SQSEvent): Promise<void> => {
  console.log('Processing compression jobs:', JSON.stringify(event, null, 2));

  for (const record of event.Records) {
    try {
      await processCompressionJob(record);
    } catch (error) {
      console.error('Error processing compression job:', error);
      // SQS will retry the message based on the queue configuration
      throw error;
    }
  }
};

// Handler for processing decompression job requests
export const decompressionJob<PERSON>andler = async (event: SQSEvent): Promise<void> => {
  console.log('Processing decompression jobs:', JSON.stringify(event, null, 2));

  for (const record of event.Records) {
    try {
      await processDecompressionJob(record);
    } catch (error) {
      console.error('Error processing decompression job:', error);
      // SQS will retry the message based on the queue configuration
      throw error;
    }
  }
};

async function processCompressionJob(record: SQSRecord): Promise<void> {
  const message = JSON.parse(record.body);
  const { transferId } = message;

  console.log(`Processing compression job for transfer: ${transferId}`);

  // Get transfer details
  const getResult = await docClient.send(new GetCommand({
    TableName: ENV.TRANSFER_TABLE,
    Key: { transferId },
  }));

  if (!getResult.Item) {
    console.error(`Transfer not found: ${transferId}`);
    return;
  }

  const transfer = getResult.Item as Transfer;

  // Create compression job record
  const jobId = generateJobId();
  const job: Job = {
    jobId,
    transferId,
    type: JobType.COMPRESSION,
    status: JobStatus.PENDING,
    createdAt: Date.now(),
    updatedAt: Date.now(),
    metadata: {
      originalFiles: transfer.originalFiles,
      originalSize: transfer.originalSize,
    },
  };

  await docClient.send(new PutCommand({
    TableName: ENV.JOB_TABLE,
    Item: job,
  }));

  // Update transfer status
  await docClient.send(new UpdateCommand({
    TableName: ENV.TRANSFER_TABLE,
    Key: { transferId },
    UpdateExpression: 'SET #status = :status, updatedAt = :updatedAt',
    ExpressionAttributeNames: { '#status': 'status' },
    ExpressionAttributeValues: { 
      ':status': TransferStatus.COMPRESSING,
      ':updatedAt': Date.now()
    },
  }));

  console.log(`Compression job created: ${jobId} for transfer: ${transferId}`);
}

async function processDecompressionJob(record: SQSRecord): Promise<void> {
  const message = JSON.parse(record.body);
  const { jobId, transferId } = message;

  console.log(`Processing decompression job: ${jobId} for transfer: ${transferId}`);

  // Update job status to processing
  await docClient.send(new UpdateCommand({
    TableName: ENV.JOB_TABLE,
    Key: { jobId },
    UpdateExpression: 'SET #status = :status, updatedAt = :updatedAt',
    ExpressionAttributeNames: { '#status': 'status' },
    ExpressionAttributeValues: { 
      ':status': JobStatus.PROCESSING,
      ':updatedAt': Date.now()
    },
  }));

  console.log(`Decompression job updated to processing: ${jobId}`);
}

// Handler for job status updates from EC2 workers
export const jobStatusUpdateHandler = async (event: any): Promise<any> => {
  try {
    console.log('Job status update:', JSON.stringify(event, null, 2));

    const { jobId, status, transferId, error, metadata } = event;

    if (!jobId || !status || !transferId) {
      throw new Error('Missing required fields: jobId, status, transferId');
    }

    // Update job status
    const updateExpression = 'SET #status = :status, updatedAt = :updatedAt';
    const expressionAttributeNames: Record<string, string> = { '#status': 'status' };
    const expressionAttributeValues: Record<string, any> = { 
      ':status': status,
      ':updatedAt': Date.now()
    };

    if (error) {
      updateExpression += ', errorMessage = :error';
      expressionAttributeValues[':error'] = error;
    }

    if (metadata) {
      updateExpression += ', metadata = :metadata';
      expressionAttributeValues[':metadata'] = metadata;
    }

    await docClient.send(new UpdateCommand({
      TableName: ENV.JOB_TABLE,
      Key: { jobId },
      UpdateExpression: updateExpression,
      ExpressionAttributeNames: expressionAttributeNames,
      ExpressionAttributeValues: expressionAttributeValues,
    }));

    // Update transfer status based on job completion
    if (status === JobStatus.COMPLETED) {
      await handleJobCompletion(jobId, transferId, metadata);
    } else if (status === JobStatus.FAILED) {
      await handleJobFailure(jobId, transferId, error);
    }

    return {
      statusCode: 200,
      body: JSON.stringify({ message: 'Job status updated successfully' }),
    };

  } catch (error) {
    console.error('Job status update error:', error);
    return {
      statusCode: 500,
      body: JSON.stringify({ error: 'Internal server error' }),
    };
  }
};

async function handleJobCompletion(jobId: string, transferId: string, metadata?: any): Promise<void> {
  // Get job details to determine type
  const jobResult = await docClient.send(new GetCommand({
    TableName: ENV.JOB_TABLE,
    Key: { jobId },
  }));

  if (!jobResult.Item) {
    console.error(`Job not found: ${jobId}`);
    return;
  }

  const job = jobResult.Item as Job;
  
  if (job.type === JobType.COMPRESSION) {
    // Update transfer with compression results
    const updateExpression = 'SET #status = :status, updatedAt = :updatedAt';
    const expressionAttributeNames: Record<string, string> = { '#status': 'status' };
    const expressionAttributeValues: Record<string, any> = { 
      ':status': TransferStatus.COMPRESSED,
      ':updatedAt': Date.now()
    };

    if (metadata?.compressedSize) {
      updateExpression += ', compressedSize = :compressedSize';
      expressionAttributeValues[':compressedSize'] = metadata.compressedSize;
    }

    if (metadata?.compressionRatio) {
      updateExpression += ', compressionRatio = :compressionRatio';
      expressionAttributeValues[':compressionRatio'] = metadata.compressionRatio;
    }

    await docClient.send(new UpdateCommand({
      TableName: ENV.TRANSFER_TABLE,
      Key: { transferId },
      UpdateExpression: updateExpression,
      ExpressionAttributeNames: expressionAttributeNames,
      ExpressionAttributeValues: expressionAttributeValues,
    }));

    console.log(`Transfer compressed successfully: ${transferId}`);

  } else if (job.type === JobType.DECOMPRESSION) {
    // Update transfer status to ready for download
    await docClient.send(new UpdateCommand({
      TableName: ENV.TRANSFER_TABLE,
      Key: { transferId },
      UpdateExpression: 'SET #status = :status, updatedAt = :updatedAt',
      ExpressionAttributeNames: { '#status': 'status' },
      ExpressionAttributeValues: { 
        ':status': TransferStatus.READY,
        ':updatedAt': Date.now()
      },
    }));

    console.log(`Transfer ready for download: ${transferId}`);
  }
}

async function handleJobFailure(jobId: string, transferId: string, error: string): Promise<void> {
  // Update transfer status to error
  await docClient.send(new UpdateCommand({
    TableName: ENV.TRANSFER_TABLE,
    Key: { transferId },
    UpdateExpression: 'SET #status = :status, updatedAt = :updatedAt, errorMessage = :error',
    ExpressionAttributeNames: { '#status': 'status' },
    ExpressionAttributeValues: { 
      ':status': TransferStatus.ERROR,
      ':updatedAt': Date.now(),
      ':error': error
    },
  }));

  console.log(`Transfer failed: ${transferId}, Error: ${error}`);
}
