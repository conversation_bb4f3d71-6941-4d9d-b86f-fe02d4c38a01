{"name": "fast-transfer-backend", "version": "1.0.0", "description": "FastTransfer backend Lambda functions", "main": "dist/index.js", "scripts": {"build": "tsc", "watch": "tsc -w", "dev": "ts-node src/server.ts", "dev:watch": "ts-node --watch src/server.ts", "test": "jest", "package": "npm run build && npm run zip", "zip": "cd dist && zip -r ../lambda-functions.zip .", "deploy": "aws lambda update-function-code --function-name FastTransfer-Upload --zip-file fileb://lambda-functions.zip"}, "dependencies": {"@aws-sdk/client-s3": "^3.400.0", "@aws-sdk/client-dynamodb": "^3.400.0", "@aws-sdk/client-sqs": "^3.400.0", "@aws-sdk/lib-dynamodb": "^3.400.0", "@aws-sdk/s3-request-presigner": "^3.400.0", "uuid": "^9.0.0", "crypto": "^1.0.1", "express": "^4.18.2", "cors": "^2.8.5", "multer": "^1.4.5-lts.1"}, "devDependencies": {"@types/aws-lambda": "^8.10.119", "@types/node": "^20.6.0", "@types/uuid": "^9.0.4", "@types/express": "^4.17.17", "@types/cors": "^2.8.13", "@types/multer": "^1.4.7", "jest": "^29.7.0", "ts-jest": "^29.1.1", "typescript": "^5.2.2", "ts-node": "^10.9.1"}}